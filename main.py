import weaviate
import weaviate.classes as wvc
import PyPDF2
import os
from pathlib import Path
from dotenv import load_dotenv
# Load environment variables
load_dotenv()
def connect_to_weaviate():
    client = weaviate.connect_to_weaviate_cloud(
        cluster_url=os.getenv("WEAVIATE_CLUSTER_URL"),
        auth_credentials=weaviate.auth.AuthApiKey(os.getenv("WEAVIATE_API_KEY")),
        headers={
            "X-OpenAI-Api-Key": os.getenv("OPENAI_API_KEY")
        }
    )
    return client
def get_or_create_collection(client, collection_name):
    if client.collections.exists(collection_name):
        collection = client.collections.get(collection_name)
        return collection
    else:
        collection = client.collections.create(
            name=collection_name,
            vectorizer_config=wvc.config.Configure.Vectorizer.text2vec_openai(
                model="text-embedding-3-small"
            ),
            generative_config=wvc.config.Configure.Generative.openai(
                model="gpt-4o-mini"
            ),
            inverted_index_config=wvc.config.Configure.inverted_index(
                index_null_state=True
            ),
            properties=[
                wvc.config.Property(
                    name="data",
                    data_type=wvc.config.DataType.TEXT,
                    tokenization=wvc.config.Tokenization.LOWERCASE,
                ),
            ],
        )
        return collection
def extract_pdf_pages(pdf_path):
    pages_text = []
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        for page_num in range(len(pdf_reader.pages)):
            page = pdf_reader.pages[page_num]
            text = page.extract_text()
            # Ensure text is properly encoded as UTF-8
            if text:
                try:
                    # For Bangla text, just use replace for any problematic characters
                    text = text.encode('utf-8', errors='replace').decode('utf-8')
                    # Don't join with spaces for Bangla as it might break words
                    cleaned_text = text.strip()
                    if cleaned_text:
                        pages_text.append(cleaned_text)
                        # Print first few characters to verify Bangla is preserved
                        if page_num == 0:
                            print(f"Sample text: {cleaned_text[:100]}")
                except UnicodeError:
                    print(f"Warning: Encoding issues on page {page_num+1}")
    return pages_text

def insert_pdf_to_weaviate(pdf_path, collection_name):
    # Connect to Weaviate Cloud
    client = connect_to_weaviate()
    try:
        # Extract pages
        pages_text = extract_pdf_pages(pdf_path)
        # Get collection
        collection = get_or_create_collection(client, collection_name)
        # Insert each page
        for page_num, page_text in enumerate(pages_text, 1):
            data_object = {
                "data": page_text
            }
            collection.data.insert(data_object)
        return len(pages_text)
    finally:
        client.close()
def main():
    PDF_PATH = "D:/pdf/Bangla_Class_9_RB.pdf"  # Update this path
    COLLECTION_NAME = "Class_9_Bangla_Book"
    total_pages = insert_pdf_to_weaviate(PDF_PATH, COLLECTION_NAME)
    print(f"Inserted {total_pages} pages successfully!")
if __name__ == "__main__":
    main()