# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: v1/aggregate.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from weaviate.proto.v1 import base_pb2 as v1_dot_base__pb2
from weaviate.proto.v1 import base_search_pb2 as v1_dot_base__search__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12v1/aggregate.proto\x12\x0bweaviate.v1\x1a\rv1/base.proto\x1a\x14v1/base_search.proto\"\xe8\x0f\n\x10\x41ggregateRequest\x12\x12\n\ncollection\x18\x01 \x01(\t\x12\x0e\n\x06tenant\x18\n \x01(\t\x12\x15\n\robjects_count\x18\x14 \x01(\x08\x12?\n\x0c\x61ggregations\x18\x15 \x03(\x0b\x32).weaviate.v1.AggregateRequest.Aggregation\x12\x19\n\x0cobject_limit\x18\x1e \x01(\rH\x01\x88\x01\x01\x12<\n\x08group_by\x18\x1f \x01(\x0b\x32%.weaviate.v1.AggregateRequest.GroupByH\x02\x88\x01\x01\x12\x12\n\x05limit\x18  \x01(\rH\x03\x88\x01\x01\x12*\n\x07\x66ilters\x18( \x01(\x0b\x32\x14.weaviate.v1.FiltersH\x04\x88\x01\x01\x12%\n\x06hybrid\x18) \x01(\x0b\x32\x13.weaviate.v1.HybridH\x00\x12.\n\x0bnear_vector\x18* \x01(\x0b\x32\x17.weaviate.v1.NearVectorH\x00\x12.\n\x0bnear_object\x18+ \x01(\x0b\x32\x17.weaviate.v1.NearObjectH\x00\x12\x30\n\tnear_text\x18, \x01(\x0b\x32\x1b.weaviate.v1.NearTextSearchH\x00\x12\x32\n\nnear_image\x18- \x01(\x0b\x32\x1c.weaviate.v1.NearImageSearchH\x00\x12\x32\n\nnear_audio\x18. \x01(\x0b\x32\x1c.weaviate.v1.NearAudioSearchH\x00\x12\x32\n\nnear_video\x18/ \x01(\x0b\x32\x1c.weaviate.v1.NearVideoSearchH\x00\x12\x32\n\nnear_depth\x18\x30 \x01(\x0b\x32\x1c.weaviate.v1.NearDepthSearchH\x00\x12\x36\n\x0cnear_thermal\x18\x31 \x01(\x0b\x32\x1e.weaviate.v1.NearThermalSearchH\x00\x12.\n\x08near_imu\x18\x32 \x01(\x0b\x32\x1a.weaviate.v1.NearIMUSearchH\x00\x1a\xde\x08\n\x0b\x41ggregation\x12\x10\n\x08property\x18\x01 \x01(\t\x12@\n\x03int\x18\x02 \x01(\x0b\x32\x31.weaviate.v1.AggregateRequest.Aggregation.IntegerH\x00\x12\x42\n\x06number\x18\x03 \x01(\x0b\x32\x30.weaviate.v1.AggregateRequest.Aggregation.NumberH\x00\x12>\n\x04text\x18\x04 \x01(\x0b\x32..weaviate.v1.AggregateRequest.Aggregation.TextH\x00\x12\x44\n\x07\x62oolean\x18\x05 \x01(\x0b\x32\x31.weaviate.v1.AggregateRequest.Aggregation.BooleanH\x00\x12>\n\x04\x64\x61te\x18\x06 \x01(\x0b\x32..weaviate.v1.AggregateRequest.Aggregation.DateH\x00\x12H\n\treference\x18\x07 \x01(\x0b\x32\x33.weaviate.v1.AggregateRequest.Aggregation.ReferenceH\x00\x1a\x81\x01\n\x07Integer\x12\r\n\x05\x63ount\x18\x01 \x01(\x08\x12\x0c\n\x04type\x18\x02 \x01(\x08\x12\x0b\n\x03sum\x18\x03 \x01(\x08\x12\x0c\n\x04mean\x18\x04 \x01(\x08\x12\x0c\n\x04mode\x18\x05 \x01(\x08\x12\x0e\n\x06median\x18\x06 \x01(\x08\x12\x0f\n\x07maximum\x18\x07 \x01(\x08\x12\x0f\n\x07minimum\x18\x08 \x01(\x08\x1a\x80\x01\n\x06Number\x12\r\n\x05\x63ount\x18\x01 \x01(\x08\x12\x0c\n\x04type\x18\x02 \x01(\x08\x12\x0b\n\x03sum\x18\x03 \x01(\x08\x12\x0c\n\x04mean\x18\x04 \x01(\x08\x12\x0c\n\x04mode\x18\x05 \x01(\x08\x12\x0e\n\x06median\x18\x06 \x01(\x08\x12\x0f\n\x07maximum\x18\x07 \x01(\x08\x12\x0f\n\x07minimum\x18\x08 \x01(\x08\x1aw\n\x04Text\x12\r\n\x05\x63ount\x18\x01 \x01(\x08\x12\x0c\n\x04type\x18\x02 \x01(\x08\x12\x16\n\x0etop_occurences\x18\x03 \x01(\x08\x12!\n\x14top_occurences_limit\x18\x04 \x01(\rH\x00\x88\x01\x01\x42\x17\n\x15_top_occurences_limit\x1a\x82\x01\n\x07\x42oolean\x12\r\n\x05\x63ount\x18\x01 \x01(\x08\x12\x0c\n\x04type\x18\x02 \x01(\x08\x12\x12\n\ntotal_true\x18\x03 \x01(\x08\x12\x13\n\x0btotal_false\x18\x04 \x01(\x08\x12\x17\n\x0fpercentage_true\x18\x05 \x01(\x08\x12\x18\n\x10percentage_false\x18\x06 \x01(\x08\x1a\x63\n\x04\x44\x61te\x12\r\n\x05\x63ount\x18\x01 \x01(\x08\x12\x0c\n\x04type\x18\x02 \x01(\x08\x12\x0e\n\x06median\x18\x03 \x01(\x08\x12\x0c\n\x04mode\x18\x04 \x01(\x08\x12\x0f\n\x07maximum\x18\x05 \x01(\x08\x12\x0f\n\x07minimum\x18\x06 \x01(\x08\x1a.\n\tReference\x12\x0c\n\x04type\x18\x01 \x01(\x08\x12\x13\n\x0bpointing_to\x18\x02 \x01(\x08\x42\r\n\x0b\x61ggregation\x1a/\n\x07GroupBy\x12\x12\n\ncollection\x18\x01 \x01(\t\x12\x10\n\x08property\x18\x02 \x01(\tB\x08\n\x06searchB\x0f\n\r_object_limitB\x0b\n\t_group_byB\x08\n\x06_limitB\n\n\x08_filters\"\xdf\x16\n\x0e\x41ggregateReply\x12\x0c\n\x04took\x18\x01 \x01(\x02\x12;\n\rsingle_result\x18\x02 \x01(\x0b\x32\".weaviate.v1.AggregateReply.SingleH\x00\x12>\n\x0fgrouped_results\x18\x03 \x01(\x0b\x32#.weaviate.v1.AggregateReply.GroupedH\x00\x1a\xc0\x0f\n\x0c\x41ggregations\x12J\n\x0c\x61ggregations\x18\x01 \x03(\x0b\x32\x34.weaviate.v1.AggregateReply.Aggregations.Aggregation\x1a\xe3\x0e\n\x0b\x41ggregation\x12\x10\n\x08property\x18\x01 \x01(\t\x12K\n\x03int\x18\x02 \x01(\x0b\x32<.weaviate.v1.AggregateReply.Aggregations.Aggregation.IntegerH\x00\x12M\n\x06number\x18\x03 \x01(\x0b\x32;.weaviate.v1.AggregateReply.Aggregations.Aggregation.NumberH\x00\x12I\n\x04text\x18\x04 \x01(\x0b\x32\x39.weaviate.v1.AggregateReply.Aggregations.Aggregation.TextH\x00\x12O\n\x07\x62oolean\x18\x05 \x01(\x0b\x32<.weaviate.v1.AggregateReply.Aggregations.Aggregation.BooleanH\x00\x12I\n\x04\x64\x61te\x18\x06 \x01(\x0b\x32\x39.weaviate.v1.AggregateReply.Aggregations.Aggregation.DateH\x00\x12S\n\treference\x18\x07 \x01(\x0b\x32>.weaviate.v1.AggregateReply.Aggregations.Aggregation.ReferenceH\x00\x1a\xf9\x01\n\x07Integer\x12\x12\n\x05\x63ount\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x11\n\x04type\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x11\n\x04mean\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x13\n\x06median\x18\x04 \x01(\x01H\x03\x88\x01\x01\x12\x11\n\x04mode\x18\x05 \x01(\x03H\x04\x88\x01\x01\x12\x14\n\x07maximum\x18\x06 \x01(\x03H\x05\x88\x01\x01\x12\x14\n\x07minimum\x18\x07 \x01(\x03H\x06\x88\x01\x01\x12\x10\n\x03sum\x18\x08 \x01(\x03H\x07\x88\x01\x01\x42\x08\n\x06_countB\x07\n\x05_typeB\x07\n\x05_meanB\t\n\x07_medianB\x07\n\x05_modeB\n\n\x08_maximumB\n\n\x08_minimumB\x06\n\x04_sum\x1a\xf8\x01\n\x06Number\x12\x12\n\x05\x63ount\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x11\n\x04type\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x11\n\x04mean\x18\x03 \x01(\x01H\x02\x88\x01\x01\x12\x13\n\x06median\x18\x04 \x01(\x01H\x03\x88\x01\x01\x12\x11\n\x04mode\x18\x05 \x01(\x01H\x04\x88\x01\x01\x12\x14\n\x07maximum\x18\x06 \x01(\x01H\x05\x88\x01\x01\x12\x14\n\x07minimum\x18\x07 \x01(\x01H\x06\x88\x01\x01\x12\x10\n\x03sum\x18\x08 \x01(\x01H\x07\x88\x01\x01\x42\x08\n\x06_countB\x07\n\x05_typeB\x07\n\x05_meanB\t\n\x07_medianB\x07\n\x05_modeB\n\n\x08_maximumB\n\n\x08_minimumB\x06\n\x04_sum\x1a\xe4\x02\n\x04Text\x12\x12\n\x05\x63ount\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x11\n\x04type\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x65\n\x0etop_occurences\x18\x03 \x01(\x0b\x32H.weaviate.v1.AggregateReply.Aggregations.Aggregation.Text.TopOccurrencesH\x02\x88\x01\x01\x1a\xa7\x01\n\x0eTopOccurrences\x12\x65\n\x05items\x18\x01 \x03(\x0b\x32V.weaviate.v1.AggregateReply.Aggregations.Aggregation.Text.TopOccurrences.TopOccurrence\x1a.\n\rTopOccurrence\x12\r\n\x05value\x18\x01 \x01(\t\x12\x0e\n\x06occurs\x18\x02 \x01(\x03\x42\x08\n\x06_countB\x07\n\x05_typeB\x11\n\x0f_top_occurences\x1a\xfb\x01\n\x07\x42oolean\x12\x12\n\x05\x63ount\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x11\n\x04type\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x17\n\ntotal_true\x18\x03 \x01(\x03H\x02\x88\x01\x01\x12\x18\n\x0btotal_false\x18\x04 \x01(\x03H\x03\x88\x01\x01\x12\x1c\n\x0fpercentage_true\x18\x05 \x01(\x01H\x04\x88\x01\x01\x12\x1d\n\x10percentage_false\x18\x06 \x01(\x01H\x05\x88\x01\x01\x42\x08\n\x06_countB\x07\n\x05_typeB\r\n\x0b_total_trueB\x0e\n\x0c_total_falseB\x12\n\x10_percentage_trueB\x13\n\x11_percentage_false\x1a\xc0\x01\n\x04\x44\x61te\x12\x12\n\x05\x63ount\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x11\n\x04type\x18\x02 \x01(\tH\x01\x88\x01\x01\x12\x13\n\x06median\x18\x03 \x01(\tH\x02\x88\x01\x01\x12\x11\n\x04mode\x18\x04 \x01(\tH\x03\x88\x01\x01\x12\x14\n\x07maximum\x18\x05 \x01(\tH\x04\x88\x01\x01\x12\x14\n\x07minimum\x18\x06 \x01(\tH\x05\x88\x01\x01\x42\x08\n\x06_countB\x07\n\x05_typeB\t\n\x07_medianB\x07\n\x05_modeB\n\n\x08_maximumB\n\n\x08_minimum\x1a<\n\tReference\x12\x11\n\x04type\x18\x01 \x01(\tH\x00\x88\x01\x01\x12\x13\n\x0bpointing_to\x18\x02 \x03(\tB\x07\n\x05_typeB\r\n\x0b\x61ggregation\x1a\x8c\x01\n\x06Single\x12\x1a\n\robjects_count\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x43\n\x0c\x61ggregations\x18\x02 \x01(\x0b\x32(.weaviate.v1.AggregateReply.AggregationsH\x01\x88\x01\x01\x42\x10\n\x0e_objects_countB\x0f\n\r_aggregations\x1a\xa7\x04\n\x05Group\x12\x1a\n\robjects_count\x18\x01 \x01(\x03H\x00\x88\x01\x01\x12\x43\n\x0c\x61ggregations\x18\x02 \x01(\x0b\x32(.weaviate.v1.AggregateReply.AggregationsH\x01\x88\x01\x01\x12\x44\n\ngrouped_by\x18\x03 \x01(\x0b\x32+.weaviate.v1.AggregateReply.Group.GroupedByH\x02\x88\x01\x01\x1a\xc4\x02\n\tGroupedBy\x12\x0c\n\x04path\x18\x01 \x03(\t\x12\x0e\n\x04text\x18\x02 \x01(\tH\x00\x12\r\n\x03int\x18\x03 \x01(\x03H\x00\x12\x11\n\x07\x62oolean\x18\x04 \x01(\x08H\x00\x12\x10\n\x06number\x18\x05 \x01(\x01H\x00\x12\'\n\x05texts\x18\x06 \x01(\x0b\x32\x16.weaviate.v1.TextArrayH\x00\x12%\n\x04ints\x18\x07 \x01(\x0b\x32\x15.weaviate.v1.IntArrayH\x00\x12-\n\x08\x62ooleans\x18\x08 \x01(\x0b\x32\x19.weaviate.v1.BooleanArrayH\x00\x12+\n\x07numbers\x18\t \x01(\x0b\x32\x18.weaviate.v1.NumberArrayH\x00\x12\x30\n\x03geo\x18\n \x01(\x0b\x32!.weaviate.v1.GeoCoordinatesFilterH\x00\x42\x07\n\x05valueB\x10\n\x0e_objects_countB\x0f\n\r_aggregationsB\r\n\x0b_grouped_by\x1a<\n\x07Grouped\x12\x31\n\x06groups\x18\x01 \x03(\x0b\x32!.weaviate.v1.AggregateReply.GroupB\x08\n\x06resultBs\n#io.weaviate.client.grpc.protocol.v1B\x16WeaviateProtoAggregateZ4github.com/weaviate/weaviate/grpc/generated;protocolb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'v1.aggregate_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n#io.weaviate.client.grpc.protocol.v1B\026WeaviateProtoAggregateZ4github.com/weaviate/weaviate/grpc/generated;protocol'
  _globals['_AGGREGATEREQUEST']._serialized_start=73
  _globals['_AGGREGATEREQUEST']._serialized_end=2097
  _globals['_AGGREGATEREQUEST_AGGREGATION']._serialized_start=868
  _globals['_AGGREGATEREQUEST_AGGREGATION']._serialized_end=1986
  _globals['_AGGREGATEREQUEST_AGGREGATION_INTEGER']._serialized_start=1308
  _globals['_AGGREGATEREQUEST_AGGREGATION_INTEGER']._serialized_end=1437
  _globals['_AGGREGATEREQUEST_AGGREGATION_NUMBER']._serialized_start=1440
  _globals['_AGGREGATEREQUEST_AGGREGATION_NUMBER']._serialized_end=1568
  _globals['_AGGREGATEREQUEST_AGGREGATION_TEXT']._serialized_start=1570
  _globals['_AGGREGATEREQUEST_AGGREGATION_TEXT']._serialized_end=1689
  _globals['_AGGREGATEREQUEST_AGGREGATION_BOOLEAN']._serialized_start=1692
  _globals['_AGGREGATEREQUEST_AGGREGATION_BOOLEAN']._serialized_end=1822
  _globals['_AGGREGATEREQUEST_AGGREGATION_DATE']._serialized_start=1824
  _globals['_AGGREGATEREQUEST_AGGREGATION_DATE']._serialized_end=1923
  _globals['_AGGREGATEREQUEST_AGGREGATION_REFERENCE']._serialized_start=1925
  _globals['_AGGREGATEREQUEST_AGGREGATION_REFERENCE']._serialized_end=1971
  _globals['_AGGREGATEREQUEST_GROUPBY']._serialized_start=1988
  _globals['_AGGREGATEREQUEST_GROUPBY']._serialized_end=2035
  _globals['_AGGREGATEREPLY']._serialized_start=2100
  _globals['_AGGREGATEREPLY']._serialized_end=5011
  _globals['_AGGREGATEREPLY_AGGREGATIONS']._serialized_start=2258
  _globals['_AGGREGATEREPLY_AGGREGATIONS']._serialized_end=4242
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION']._serialized_start=2351
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION']._serialized_end=4242
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_INTEGER']._serialized_start=2857
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_INTEGER']._serialized_end=3106
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_NUMBER']._serialized_start=3109
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_NUMBER']._serialized_end=3357
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_TEXT']._serialized_start=3360
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_TEXT']._serialized_end=3716
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_TEXT_TOPOCCURRENCES']._serialized_start=3511
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_TEXT_TOPOCCURRENCES']._serialized_end=3678
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_TEXT_TOPOCCURRENCES_TOPOCCURRENCE']._serialized_start=3632
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_TEXT_TOPOCCURRENCES_TOPOCCURRENCE']._serialized_end=3678
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_BOOLEAN']._serialized_start=3719
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_BOOLEAN']._serialized_end=3970
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_DATE']._serialized_start=3973
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_DATE']._serialized_end=4165
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_REFERENCE']._serialized_start=4167
  _globals['_AGGREGATEREPLY_AGGREGATIONS_AGGREGATION_REFERENCE']._serialized_end=4227
  _globals['_AGGREGATEREPLY_SINGLE']._serialized_start=4245
  _globals['_AGGREGATEREPLY_SINGLE']._serialized_end=4385
  _globals['_AGGREGATEREPLY_GROUP']._serialized_start=4388
  _globals['_AGGREGATEREPLY_GROUP']._serialized_end=4939
  _globals['_AGGREGATEREPLY_GROUP_GROUPEDBY']._serialized_start=4565
  _globals['_AGGREGATEREPLY_GROUP_GROUPEDBY']._serialized_end=4889
  _globals['_AGGREGATEREPLY_GROUPED']._serialized_start=4941
  _globals['_AGGREGATEREPLY_GROUPED']._serialized_end=5001
# @@protoc_insertion_point(module_scope)
