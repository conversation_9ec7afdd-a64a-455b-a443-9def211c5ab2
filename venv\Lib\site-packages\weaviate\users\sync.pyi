from typing import Dict, List, Literal, Optional, Union, overload

from typing_extensions import deprecated

from weaviate.connect.v4 import ConnectionSync
from weaviate.rbac.models import Role, RoleBase
from weaviate.users.base import _BaseExecutor, _UsersDBExecutor, _UsersExecutor, _UsersOIDCExecutor
from weaviate.users.users import USER_TYPE, OwnUser, UserDB

class _Base(_BaseExecutor[ConnectionSync]):
    def _get_roles_of_user(
        self, user_id: str, user_type: USER_TYPE, include_permissions: bool
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    def _get_roles_of_user_deprecated(
        self, user_id: str
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    def _assign_roles_to_user(
        self, roles: List[str], user_id: str, user_type: Optional[USER_TYPE]
    ) -> None: ...
    def _revoke_roles_from_user(
        self, roles: Union[str, List[str]], user_id: str, user_type: Optional[USER_TYPE]
    ) -> None: ...

class _UsersOIDC(_UsersOIDCExecutor[ConnectionSync]):
    @overload
    def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[False] = False
    ) -> Dict[str, RoleBase]: ...
    @overload
    def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[True]
    ) -> Dict[str, Role]: ...
    @overload
    def get_assigned_roles(
        self, *, user_id: str, include_permissions: bool = False
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    def assign_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    def revoke_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...

class _UsersDB(_UsersDBExecutor[ConnectionSync]):
    @overload
    def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[False] = False
    ) -> Dict[str, RoleBase]: ...
    @overload
    def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[True]
    ) -> Dict[str, Role]: ...
    @overload
    def get_assigned_roles(
        self, *, user_id: str, include_permissions: bool = False
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    def assign_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    def revoke_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    def create(self, *, user_id: str) -> str: ...
    def delete(self, *, user_id: str) -> bool: ...
    def rotate_key(self, *, user_id: str) -> str: ...
    def activate(self, *, user_id: str) -> bool: ...
    def deactivate(self, *, user_id: str, revoke_key: bool = False) -> bool: ...
    def get(self, *, user_id: str) -> Optional[UserDB]: ...
    def list_all(self) -> List[UserDB]: ...

class _Users(_UsersExecutor[ConnectionSync]):
    def get_my_user(self) -> OwnUser: ...
    @deprecated(
        "This method is deprecated and will be removed in Q4 25.\n                Please use `users.db.get_assigned_roles` and/or `users.oidc.get_assigned_roles` instead."
    )
    def get_assigned_roles(self, user_id: str) -> Dict[str, Role]: ...
    @deprecated(
        "This method is deprecated and will be removed in Q4 25.\n                Please use `users.db.assign_roles` and/or `users.oidc.assign_roles` instead."
    )
    def assign_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    @deprecated(
        "This method is deprecated and will be removed in Q4 25.\n                Please use `users.db.revoke_roles` and/or `users.oidc.revoke_roles` instead."
    )
    def revoke_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    @property
    def oidc(self) -> _UsersOIDC: ...
    @property
    def db(self) -> _UsersDB: ...
